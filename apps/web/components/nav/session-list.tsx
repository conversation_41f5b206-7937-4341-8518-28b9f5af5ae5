"use client"

import { useState } from "react"
import { Trash2, Edit2 } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@ragtop-web/ui/components/button"
import {
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@ragtop-web/ui/components/sidebar"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@ragtop-web/ui/components/alert-dialog"
import { useSessions, useDeleteSession, useUpdateSession, Sessions } from "@/service/session-service"
import { useToast } from "@ragtop-web/ui/components/use-toast"
import { Input } from "@ragtop-web/ui/components/input"

interface SessionListProps {
  agentId: string
  isExpanded: boolean
}

/**
 * Session列表组件
 * 显示指定Agent下的所有sessions
 */
export function SessionList({ agentId, isExpanded }: SessionListProps) {
  const pathname = usePathname()
  const { toast } = useToast()
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null)
  const [editingTitle, setEditingTitle] = useState("")
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [sessionToDelete, setSessionToDelete] = useState<Sessions | null>(null)

  // 获取sessions数据
  const { data: sessionsData, isLoading } = useSessions(agentId, 1, 20)
  const sessions = sessionsData?.records || []

  // API hooks
  const deleteSession = useDeleteSession()
  const updateSession = useUpdateSession(agentId)

  // 检查session是否被选中
  const isSessionSelected = (sessionId: string) => {
    return pathname === `/agent/${agentId}/${sessionId}`
  }

  // 打开删除确认对话框
  const openDeleteDialog = (session: Sessions) => {
    setSessionToDelete(session)
    setDeleteDialogOpen(true)
  }

  // 关闭删除确认对话框
  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false)
    setSessionToDelete(null)
  }

  // 处理删除session
  const handleDeleteSession = async () => {
    if (!sessionToDelete) return

    try {
      await deleteSession.mutateAsync({ session_id: sessionToDelete.id })
      toast({
        title: "成功",
        description: "会话删除成功",
      })
      closeDeleteDialog()
    } catch (error) {
      console.error("删除会话失败:", error)
      toast({
        title: "错误",
        description: "删除会话失败",
        variant: "destructive",
      })
    }
  }

  // 开始重命名
  const startRename = (session: Sessions) => {
    setEditingSessionId(session.id)
    setEditingTitle(session.title)
  }

  // 取消重命名
  const cancelRename = () => {
    setEditingSessionId(null)
    setEditingTitle("")
  }

  // 保存重命名
  const saveRename = async (sessionId: string) => {
    if (!editingTitle.trim()) {
      toast({
        title: "错误",
        description: "会话名称不能为空",
        variant: "destructive",
      })
      return
    }

    try {
      await updateSession.mutateAsync({
        session_id: sessionId,
        title: editingTitle.trim()
      })
      toast({
        title: "成功",
        description: "会话重命名成功",
      })
      setEditingSessionId(null)
      setEditingTitle("")
    } catch (error) {

      console.error("重命名会话失败:", error)
      toast({
        title: "错误",
        description: "重命名会话失败",
        variant: "destructive",
      })
    }
  }

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent, sessionId: string) => {
    if (e.key === "Enter") {
      saveRename(sessionId)
    } else if (e.key === "Escape") {
      cancelRename()
    }
  }

  // 如果没有展开，不渲染
  if (!isExpanded) {
    return null
  }

  // 如果正在加载
  if (isLoading) {
    return (
      <SidebarMenuSub>
        <SidebarMenuSubItem>
          <SidebarMenuSubButton>
            <span className="text-muted-foreground text-sm">加载中...</span>
          </SidebarMenuSubButton>
        </SidebarMenuSubItem>
      </SidebarMenuSub>
    )
  }

  // 如果没有sessions
  if (sessions.length === 0) {
    return (
      <SidebarMenuSub>
        <SidebarMenuSubItem>
          <SidebarMenuSubButton>
            <span className="text-muted-foreground text-sm">暂无会话</span>
          </SidebarMenuSubButton>
        </SidebarMenuSubItem>
      </SidebarMenuSub>
    )
  }

  return (
    <>
      <SidebarMenuSub>
        {sessions.map((session) => (
          <SidebarMenuSubItem key={session.id} className="group/sub-item relative">
            {editingSessionId === session.id ? (
              // 重命名模式
              <div className="flex items-center px-2 py-1">
                <Input
                  value={editingTitle}
                  onChange={(e) => setEditingTitle(e.target.value)}
                  onKeyDown={(e) => handleKeyDown(e, session.id)}
                  onBlur={() => saveRename(session.id)}
                  className="h-6 text-sm"
                  autoFocus
                />
              </div>
            ) : (
              // 正常显示模式
              <SidebarMenuSubButton
                asChild
                isActive={isSessionSelected(session.id)}
                onDoubleClick={() => startRename(session)}
              >
                <Link href={`/agent/${agentId}/${session.id}`}>
                  <span className="truncate">{session.title}</span>
                </Link>
              </SidebarMenuSubButton>
            )}

            {/* 悬停时显示的操作按钮 */}
            <div className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover/sub-item:opacity-100 transition-opacity flex gap-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 p-0.5 text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  startRename(session)
                }}
                aria-label={`重命名 ${session.title}`}
              >
                <Edit2 className="h-3.5 w-3.5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 p-0.5 text-destructive hover:bg-destructive/10 hover:text-destructive"
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  openDeleteDialog(session)
                }}
                aria-label={`删除 ${session.title}`}
              >
                <Trash2 className="h-3.5 w-3.5" />
                <span className="sr-only">删除 {session.title}</span>
              </Button>
            </div>
          </SidebarMenuSubItem>
        ))}
      </SidebarMenuSub>

      {/* 删除确认对话框 */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除会话</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除会话 "{sessionToDelete?.title}" 吗？此操作无法撤销，会话中的所有消息都将被永久删除。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={closeDeleteDialog}>
              取消
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteSession}
              disabled={deleteSession.isPending}
            >
              {deleteSession.isPending ? "删除中..." : "确认删除"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
